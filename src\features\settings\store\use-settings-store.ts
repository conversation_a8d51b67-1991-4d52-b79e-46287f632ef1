'use client';

import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { useShallow } from 'zustand/react/shallow';

// UI-only state interface - server state moved to React Query
interface SettingsState {
  isSettingsOpen: boolean;
  activeSection: string;
}

// UI-only actions interface - server state actions moved to React Query
interface SettingsActions {
  openSettings: (section?: string) => void;
  closeSettings: () => void;
  setActiveSection: (section: string) => void;
}

const initialState: SettingsState = {
  isSettingsOpen: false,
  activeSection: 'profile',
};

// Modern UI-only Zustand store - server state handled by React Query
export const useSettingsStore = create<SettingsState & SettingsActions>()(
  devtools(
    persist(
      (set) => ({
        ...initialState,

        // UI actions only - server state handled by React Query
        openSettings: (section = 'profile') => {
          set({ isSettingsOpen: true, activeSection: section });
        },

        closeSettings: () => {
          set({ isSettingsOpen: false });
        },

        setActiveSection: (section: string) => {
          set({ activeSection: section });
        },
      }),
      {
        name: 'settings-ui-store',
        partialize: (state) => ({
          activeSection: state.activeSection,
        }),
      }
    ),
    { name: 'SettingsUIStore' }
  )
);

// Selector hooks for UI state only
export const useSettingsOpen = () =>
  useSettingsStore((state) => state.isSettingsOpen);

export const useActiveSection = () =>
  useSettingsStore((state) => state.activeSection);

export const useSettingsUIState = () =>
  useSettingsStore(
    useShallow((state) => ({
      isSettingsOpen: state.isSettingsOpen,
      activeSection: state.activeSection,
    }))
  );

export const useSettingsActions = () =>
  useSettingsStore((state) => ({
    openSettings: state.openSettings,
    closeSettings: state.closeSettings,
    setActiveSection: state.setActiveSection,
  }));

// Legacy compatibility selectors - return null/false for server state
// Components using these will need to be updated to use React Query hooks
export const useUserSettings = () => null;
export const useAdminSettings = () => null;
export const useSettingsLoading = () => false;
export const useSettingsError = () => null;

// Combined selectors for backward compatibility
export const useSettingsData = () => ({
  userSettings: null,
  adminSettings: null,
  isLoading: false,
  error: null,
});

// 2025 Performance-optimized selectors using useShallow
export const useSettingsOptimized = {
  // Combined UI state selector
  useSettingsUIState: () =>
    useSettingsStore(
      useShallow((state) => ({
        isSettingsOpen: state.isSettingsOpen,
        activeSection: state.activeSection,
      }))
    ),

  // Combined actions selector
  useSettingsActions: () =>
    useSettingsStore(
      useShallow((state) => ({
        openSettings: state.openSettings,
        closeSettings: state.closeSettings,
        setActiveSection: state.setActiveSection,
      }))
    ),
};
