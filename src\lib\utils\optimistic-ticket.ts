/**
 * Optimistic Ticket Utilities - 2025 Optimized
 *
 * Centralized utilities for handling optimistic ticket operations
 * Eliminates duplicate logic across multiple hooks
 */

/**
 * Checks if a ticket ID represents an optimistic/temporary ticket
 * Consolidates duplicate logic from useTicket, useRealtimeTicket, etc.
 */
export function isOptimisticTicket(
  ticketId: string | null | undefined
): boolean {
  if (!ticketId) return false;
  return ticketId.startsWith('optimistic-') || ticketId.startsWith('temp-');
}

/**
 * Generates a unique optimistic ticket ID
 */
export function generateOptimisticTicketId(): string {
  return `optimistic-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
}

/**
 * Extracts the real ticket ID from an optimistic ticket if available
 * Returns null if it's still optimistic
 */
export function extractRealTicketId(ticketId: string): string | null {
  if (!isOptimisticTicket(ticketId)) {
    return ticketId;
  }
  return null;
}

/**
 * Checks if a ticket should be excluded from API calls
 * Used to prevent unnecessary API calls for optimistic tickets
 */
export function shouldSkipApiCall(
  ticketId: string | null | undefined
): boolean {
  return isOptimisticTicket(ticketId);
}
