/**
 * Centralized API Client - 2025 Optimized
 *
 * Eliminates duplicate fetch calls across the application
 * Provides consistent error handling and request configuration
 */

interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

interface RequestConfig {
  method?: 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE';
  headers?: Record<string, string>;
  body?: any;
  params?: Record<string, string>;
}

class ApiClient {
  private baseUrl: string;
  private defaultHeaders: Record<string, string>;

  constructor(baseUrl = '/api') {
    this.baseUrl = baseUrl;
    this.defaultHeaders = {
      'Content-Type': 'application/json',
    };
  }

  private buildUrl(endpoint: string, params?: Record<string, string>): string {
    const url = new URL(endpoint, window.location.origin + this.baseUrl);

    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          url.searchParams.append(key, value);
        }
      });
    }

    return url.toString();
  }

  private async request<T>(
    endpoint: string,
    config: RequestConfig = {}
  ): Promise<ApiResponse<T>> {
    const { method = 'GET', headers = {}, body, params } = config;

    const url = this.buildUrl(endpoint, params);
    const requestHeaders = { ...this.defaultHeaders, ...headers };

    try {
      const response = await fetch(url, {
        method,
        headers: requestHeaders,
        ...(body && { body: JSON.stringify(body) }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          errorData.error ||
            errorData.message ||
            `HTTP ${response.status}: ${response.statusText}`
        );
      }

      const data = await response.json();
      return {
        success: true,
        data: data.data || data,
        message: data.message,
      };
    } catch (error) {
      console.error(`API Error [${method} ${endpoint}]:`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  // Ticket API methods
  async getTickets(tenantId: string, filters?: any) {
    return this.request('/tickets', {
      params: { tenant_id: tenantId, ...filters },
    });
  }

  async getTicket(tenantId: string, ticketId: string) {
    return this.request(`/tickets/${ticketId}`, {
      params: { tenant_id: tenantId },
    });
  }

  async getTicketMessages(tenantId: string, ticketId: string) {
    return this.request(`/tickets/${ticketId}/messages`, {
      params: { tenant_id: tenantId },
    });
  }

  async createTicket(tenantId: string, data: any) {
    return this.request('/tickets', {
      method: 'POST',
      body: { ...data, tenant_id: tenantId },
    });
  }

  async updateTicket(tenantId: string, ticketId: string, data: any) {
    return this.request(`/tickets/${ticketId}`, {
      method: 'PUT',
      body: { ...data, tenant_id: tenantId },
    });
  }

  async openTicket(tenantId: string, ticketId: string) {
    return this.request(`/tickets/${ticketId}/open`, {
      method: 'POST',
      body: { tenant_id: tenantId },
    });
  }

  // User API methods
  async searchUsers(tenantId: string, query: string, limit = 10) {
    return this.request('/users/search', {
      params: { tenant_id: tenantId, q: query, limit: limit.toString() },
    });
  }

  async getUserInfo() {
    return this.request('/users/me');
  }

  // Tenant API methods
  async resolveTenant(subdomain: string) {
    return this.request('/tenants/resolve', {
      params: { subdomain },
    });
  }

  // Settings API methods
  async getSettings() {
    return this.request('/settings');
  }

  async updateUserSettings(updates: Record<string, unknown>) {
    return this.request('/settings', {
      method: 'PUT',
      body: { type: 'user', ...updates },
    });
  }

  async updateAdminSettings(updates: Record<string, unknown>) {
    return this.request('/settings/admin', {
      method: 'PUT',
      body: updates,
    });
  }
}

// Export singleton instance
export const apiClient = new ApiClient();

// Export class for testing or custom instances
export { ApiClient };
export type { ApiResponse, RequestConfig };
