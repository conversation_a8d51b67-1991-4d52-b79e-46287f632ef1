/**
 * Centralized Validation Utilities - 2025 Optimized
 * 
 * Consolidates duplicate validation logic across the application
 * Provides reusable validation functions for forms, inputs, and data
 */

import { z } from 'zod';

/**
 * Common validation patterns used across the application
 */
export const ValidationPatterns = {
  // Email validation
  email: z.string().email('Please enter a valid email address'),
  
  // Required string with minimum length
  requiredString: (minLength = 1, fieldName = 'This field') =>
    z.string()
      .min(minLength, `${fieldName} must be at least ${minLength} character${minLength > 1 ? 's' : ''} long`)
      .trim(),
  
  // Optional string with maximum length
  optionalString: (maxLength = 1000) =>
    z.string().max(maxLength, `Must be ${maxLength} characters or less`).optional(),
  
  // Ticket title validation
  ticketTitle: z.string()
    .min(3, 'Title must be at least 3 characters long')
    .max(200, 'Title must be 200 characters or less')
    .trim(),
  
  // Ticket description validation
  ticketDescription: z.string()
    .min(10, 'Description must be at least 10 characters long')
    .max(10000, 'Description must be 10,000 characters or less')
    .trim(),
  
  // Department name validation
  departmentName: z.string()
    .min(2, 'Department name must be at least 2 characters long')
    .max(50, 'Department name must be 50 characters or less')
    .trim(),
  
  // Color validation (hex color)
  hexColor: z.string()
    .regex(/^#[0-9A-F]{6}$/i, 'Must be a valid hex color (e.g., #FF0000)'),
  
  // UUID validation
  uuid: z.string().uuid('Must be a valid UUID'),
  
  // Tenant ID validation (subdomain format)
  tenantId: z.string()
    .min(3, 'Tenant ID must be at least 3 characters long')
    .max(63, 'Tenant ID must be 63 characters or less')
    .regex(/^[a-z0-9-]+$/, 'Tenant ID can only contain lowercase letters, numbers, and hyphens')
    .refine(val => !val.startsWith('-') && !val.endsWith('-'), 'Tenant ID cannot start or end with a hyphen'),
  
  // Priority validation
  priority: z.enum(['low', 'medium', 'high', 'urgent'], {
    errorMap: () => ({ message: 'Priority must be low, medium, high, or urgent' }),
  }),
  
  // Status validation
  ticketStatus: z.enum(['open', 'in_progress', 'resolved', 'closed'], {
    errorMap: () => ({ message: 'Status must be open, in_progress, resolved, or closed' }),
  }),
  
  // Role validation
  userRole: z.enum(['user', 'agent', 'admin', 'super_admin'], {
    errorMap: () => ({ message: 'Role must be user, agent, admin, or super_admin' }),
  }),
} as const;

/**
 * Validation functions for specific use cases
 */
export const ValidationFunctions = {
  /**
   * Validates if a string is a valid optimistic ticket ID
   */
  isOptimisticTicketId: (ticketId: string): boolean => {
    return ticketId.startsWith('optimistic-') || ticketId.startsWith('temp-');
  },

  /**
   * Validates department existence and status
   */
  isDepartmentValid: (departmentId: string, activeDepartments: any[]): boolean => {
    return activeDepartments.some(dept => dept.id === departmentId && dept.is_active);
  },

  /**
   * Validates user permission for action
   */
  hasPermission: (userRole: string, requiredRole: string): boolean => {
    const roleHierarchy = ['user', 'agent', 'admin', 'super_admin'];
    const userRoleIndex = roleHierarchy.indexOf(userRole);
    const requiredRoleIndex = roleHierarchy.indexOf(requiredRole);
    
    return userRoleIndex >= requiredRoleIndex;
  },

  /**
   * Validates file upload constraints
   */
  validateFileUpload: (file: File, maxSizeMB = 10, allowedTypes: string[] = []): string | null => {
    // Size validation
    const maxSizeBytes = maxSizeMB * 1024 * 1024;
    if (file.size > maxSizeBytes) {
      return `File size must be less than ${maxSizeMB}MB`;
    }

    // Type validation
    if (allowedTypes.length > 0 && !allowedTypes.includes(file.type)) {
      return `File type not allowed. Allowed types: ${allowedTypes.join(', ')}`;
    }

    return null; // Valid
  },

  /**
   * Validates HTML content for safety
   */
  isHtmlSafe: (html: string): boolean => {
    // Basic check for potentially dangerous tags
    const dangerousTags = /<script|<iframe|<object|<embed|<link|<meta|<style/i;
    const dangerousAttributes = /on\w+\s*=/i; // onclick, onload, etc.
    
    return !dangerousTags.test(html) && !dangerousAttributes.test(html);
  },

  /**
   * Validates word count within limits
   */
  validateWordCount: (text: string, minWords = 0, maxWords = Infinity): string | null => {
    const wordCount = text.trim().split(/\s+/).filter(word => word.length > 0).length;
    
    if (wordCount < minWords) {
      return `Must contain at least ${minWords} word${minWords > 1 ? 's' : ''}`;
    }
    
    if (wordCount > maxWords) {
      return `Must contain no more than ${maxWords} word${maxWords > 1 ? 's' : ''}`;
    }
    
    return null; // Valid
  },

  /**
   * Validates URL format
   */
  isValidUrl: (url: string): boolean => {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  },

  /**
   * Validates tenant subdomain format
   */
  isValidSubdomain: (subdomain: string): boolean => {
    // RFC 1123 compliant subdomain validation
    const subdomainRegex = /^[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?$/;
    return subdomainRegex.test(subdomain) && subdomain !== 'www' && subdomain !== 'api';
  },
} as const;

/**
 * Form validation schemas for common forms
 */
export const FormSchemas = {
  // Create ticket form
  createTicket: z.object({
    title: ValidationPatterns.ticketTitle,
    description: ValidationPatterns.ticketDescription,
    priority: ValidationPatterns.priority,
    department_id: ValidationPatterns.uuid,
    attachment_ids: z.array(ValidationPatterns.uuid).optional(),
  }),

  // Update ticket form
  updateTicket: z.object({
    title: ValidationPatterns.ticketTitle.optional(),
    description: ValidationPatterns.ticketDescription.optional(),
    priority: ValidationPatterns.priority.optional(),
    status: ValidationPatterns.ticketStatus.optional(),
    assigned_to: ValidationPatterns.uuid.optional(),
  }),

  // Create department form
  createDepartment: z.object({
    name: ValidationPatterns.departmentName,
    color: ValidationPatterns.hexColor,
    dot_color: ValidationPatterns.hexColor,
    icon: ValidationPatterns.requiredString(1, 'Icon'),
  }),

  // User settings form
  userSettings: z.object({
    email_notifications: z.boolean().optional(),
    push_notifications: z.boolean().optional(),
    theme: z.enum(['light', 'dark', 'system']).optional(),
    language: z.string().optional(),
  }),

  // Admin settings form
  adminSettings: z.object({
    auto_assignment: z.boolean().optional(),
    require_approval: z.boolean().optional(),
    max_tickets_per_user: z.number().min(1).max(1000).optional(),
    default_priority: ValidationPatterns.priority.optional(),
  }),
} as const;

/**
 * Validation error formatting utilities
 */
export const ValidationUtils = {
  /**
   * Formats Zod validation errors for display
   */
  formatZodErrors: (error: z.ZodError): Record<string, string> => {
    const formattedErrors: Record<string, string> = {};
    
    error.errors.forEach(err => {
      const path = err.path.join('.');
      formattedErrors[path] = err.message;
    });
    
    return formattedErrors;
  },

  /**
   * Checks if form data is valid without throwing
   */
  isValidData: <T>(schema: z.ZodSchema<T>, data: unknown): data is T => {
    try {
      schema.parse(data);
      return true;
    } catch {
      return false;
    }
  },

  /**
   * Safely parses data with default fallback
   */
  safeParseWithDefault: <T>(
    schema: z.ZodSchema<T>, 
    data: unknown, 
    defaultValue: T
  ): T => {
    try {
      return schema.parse(data);
    } catch {
      return defaultValue;
    }
  },

  /**
   * Validates and sanitizes HTML content
   */
  sanitizeAndValidateHtml: (html: string): { isValid: boolean; sanitized: string; errors: string[] } => {
    const errors: string[] = [];
    let sanitized = html;

    // Check for dangerous content
    if (!ValidationFunctions.isHtmlSafe(html)) {
      errors.push('HTML content contains potentially unsafe elements');
    }

    // Basic sanitization (remove script tags, etc.)
    sanitized = html
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
      .replace(/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi, '')
      .replace(/on\w+\s*=\s*["'][^"']*["']/gi, ''); // Remove event handlers

    return {
      isValid: errors.length === 0,
      sanitized,
      errors,
    };
  },
} as const;
