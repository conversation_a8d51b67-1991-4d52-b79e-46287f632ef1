/**
 * Tenant UUID Resolution Utilities - 2025 Optimized
 *
 * Centralized utilities for tenant UUID resolution
 * Eliminates duplicate patterns across hooks and components
 */

import { useQuery } from '@tanstack/react-query';

/**
 * Centralized tenant UUID resolution hook
 * Replaces duplicate patterns across multiple hooks
 */
export function useTenantUuid(tenantId: string | null) {
  return useQuery({
    queryKey: ['tenant', 'uuid', tenantId],
    queryFn: async () => {
      if (!tenantId) throw new Error('Tenant ID is required');

      const response = await fetch(
        `/api/tenants/resolve?subdomain=${tenantId}`
      );
      if (!response.ok) {
        throw new Error('Failed to resolve tenant UUID');
      }

      const data = await response.json();
      return data.tenant?.id || null;
    },
    enabled: !!tenantId,
    staleTime: 60 * 60 * 1000, // 1 hour - tenant data rarely changes
    gcTime: 24 * 60 * 60 * 1000, // 24 hours
  });
}

/**
 * Synchronous tenant UUID getter for cases where we need immediate access
 * Should be used sparingly and only when the UUID is already cached
 */
export function getTenantUuidFromCache(
  queryClient: any,
  tenantId: string | null
): string | null {
  if (!tenantId) return null;

  const queryKey = ['tenant', 'uuid', tenantId];
  return queryClient.getQueryData(queryKey) || null;
}
