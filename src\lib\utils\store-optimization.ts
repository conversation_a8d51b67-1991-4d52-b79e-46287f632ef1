/**
 * Store Optimization Utilities - 2025 Patterns
 *
 * Utilities for optimizing Zustand stores and eliminating server state duplication
 * Provides patterns for proper state slicing and performance optimization
 */

import { useShallow } from 'zustand/react/shallow';
import { useCallback, useMemo } from 'react';

/**
 * Creates a stable selector function that prevents unnecessary re-renders
 * Use this for complex selectors that return objects or arrays
 */
export function createStableSelector<T, R>(
  selector: (state: T) => R,
  deps: any[] = []
): (state: T) => R {
  // eslint-disable-next-line react-hooks/exhaustive-deps
  return useCallback(selector, deps);
}

/**
 * Creates a memoized shallow selector for multi-property selections
 * Prevents re-renders when selecting multiple properties from store
 */
export function createShallowSelector<T, R>(
  selector: (state: T) => R
): (state: T) => R {
  return useCallback((state: T) => useShallow(selector)(state), [selector]);
}

/**
 * Utility to check if server state exists in Zustand store
 * Helps identify stores that need to be refactored to use React Query
 */
export function detectServerState(storeState: Record<string, any>): string[] {
  const serverStateKeys: string[] = [];

  // Common patterns that indicate server state
  const serverStatePatterns = [
    /.*data$/i, // ends with 'data'
    /.*list$/i, // ends with 'list'
    /.*items$/i, // ends with 'items'
    /.*cache$/i, // ends with 'cache'
    /^tickets$/i, // tickets array
    /^users$/i, // users array
    /^messages$/i, // messages array
    /^settings$/i, // settings object
    /.*Settings$/i, // ends with 'Settings'
    /.*Config$/i, // ends with 'Config'
  ];

  Object.keys(storeState).forEach((key) => {
    const value = storeState[key];

    // Check if key matches server state patterns
    const isServerStateKey = serverStatePatterns.some((pattern) =>
      pattern.test(key)
    );

    // Check if value looks like server data
    const isServerStateValue =
      (Array.isArray(value) &&
        value.length > 0 &&
        typeof value[0] === 'object' &&
        value[0].id) ||
      (typeof value === 'object' &&
        value !== null &&
        ('id' in value || 'created_at' in value || 'updated_at' in value));

    if (isServerStateKey || isServerStateValue) {
      serverStateKeys.push(key);
    }
  });

  return serverStateKeys;
}

/**
 * Creates optimized selectors for UI-only state
 * Ensures stores only contain UI state, not server state
 */
export function createUIOnlySelectors<T extends Record<string, any>>(
  useStore: (selector: (state: T) => any) => any
) {
  return {
    // Single property selectors (most performant)
    useSingle: <K extends keyof T>(key: K) =>
      useStore((state: T) => state[key]),

    // Multi-property selector with shallow comparison
    useMultiple: <K extends keyof T>(keys: K[]) =>
      useStore(
        useShallow((state: T) =>
          keys.reduce(
            (acc, key) => {
              acc[key] = state[key];
              return acc;
            },
            {} as Pick<T, K>
          )
        )
      ),

    // Actions selector with shallow comparison
    useActions: (actionKeys: (keyof T)[]) =>
      useStore(
        useShallow((state: T) =>
          actionKeys.reduce(
            (acc, key) => {
              acc[key] = state[key];
              return acc;
            },
            {} as Pick<T, keyof T>
          )
        )
      ),
  };
}

/**
 * Performance monitoring utility for store subscriptions
 * Helps identify stores causing unnecessary re-renders
 */
export function createStorePerformanceMonitor<T>(
  storeName: string,
  useStore: (selector: (state: T) => any) => any
) {
  let renderCount = 0;
  let lastRenderTime = Date.now();

  return {
    // Wrapped selector that tracks render frequency
    useMonitoredSelector: <R>(
      selector: (state: T) => R,
      selectorName = 'unknown'
    ) => {
      renderCount++;
      const currentTime = Date.now();
      const timeSinceLastRender = currentTime - lastRenderTime;

      if (process.env.NODE_ENV === 'development') {
        if (timeSinceLastRender < 16) {
          // Less than one frame (60fps)
          console.warn(
            `🚨 ${storeName} store: Rapid re-renders detected for selector "${selectorName}". ` +
              `Render #${renderCount}, ${timeSinceLastRender}ms since last render.`
          );
        }
      }

      lastRenderTime = currentTime;
      return useStore(selector);
    },

    // Get performance stats
    getStats: () => ({
      renderCount,
      lastRenderTime,
      storeName,
    }),

    // Reset stats
    resetStats: () => {
      renderCount = 0;
      lastRenderTime = Date.now();
    },
  };
}

/**
 * Utility to migrate server state from Zustand to React Query
 * Provides guidance on what needs to be moved
 */
export function analyzeStoreForMigration<T extends Record<string, any>>(
  storeState: T,
  storeName: string
): {
  uiState: Partial<T>;
  serverState: Partial<T>;
  recommendations: string[];
} {
  const serverStateKeys = detectServerState(storeState);
  const recommendations: string[] = [];

  const uiState: Partial<T> = {};
  const serverState: Partial<T> = {};

  Object.entries(storeState).forEach(([key, value]) => {
    if (serverStateKeys.includes(key)) {
      serverState[key as keyof T] = value;
      recommendations.push(
        `Move "${key}" to React Query hook (e.g., use${key.charAt(0).toUpperCase() + key.slice(1)})`
      );
    } else {
      uiState[key as keyof T] = value;
    }
  });

  if (serverStateKeys.length > 0) {
    recommendations.unshift(
      `${storeName} store contains ${serverStateKeys.length} server state properties that should be moved to React Query`
    );
  }

  return {
    uiState,
    serverState,
    recommendations,
  };
}

/**
 * Type-safe store subscription utility
 * Provides better TypeScript support for store subscriptions
 */
export function createTypedStoreSubscription<T>(useStore: any) {
  return {
    subscribe: <R>(
      selector: (state: T) => R,
      callback: (value: R, previousValue: R) => void
    ) => {
      let previousValue = selector(useStore.getState());

      return useStore.subscribe((state: T) => {
        const currentValue = selector(state);
        if (currentValue !== previousValue) {
          callback(currentValue, previousValue);
          previousValue = currentValue;
        }
      });
    },
  };
}
